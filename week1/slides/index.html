<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第1周：Web开发概述与环境搭建</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="presentation">
        <!-- 封面页 -->
        <section class="slide active" id="slide-1">
            <div class="slide-content">
                <h1>Web程序设计</h1>
                <h2>第1周：Web开发概述与环境搭建</h2>
                <div class="course-info">
                    <p>课程目标：</p>
                    <ul>
                        <li>了解Web发展历史与基本原理</li>
                        <li>掌握HTTP协议基础知识</li>
                        <li>配置Web开发环境</li>
                        <li>创建第一个HTML页面</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Web发展历史 -->
        <section class="slide" id="slide-2">
            <div class="slide-content">
                <h2>Web发展历史</h2>
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="year">1989</div>
                        <div class="content">Tim Berners-Lee 发明万维网(WWW)</div>
                    </div>
                    <div class="timeline-item">
                        <div class="year">1993</div>
                        <div class="content">第一个图形化浏览器 Mosaic 发布</div>
                    </div>
                    <div class="timeline-item">
                        <div class="year">1995</div>
                        <div class="content">JavaScript 诞生，网页开始具备交互性</div>
                    </div>
                    <div class="timeline-item">
                        <div class="year">2004</div>
                        <div class="content">AJAX 技术兴起，Web 2.0 时代开始</div>
                    </div>
                    <div class="timeline-item">
                        <div class="year">2008</div>
                        <div class="content">HTML5 标准制定，现代Web开发开始</div>
                    </div>
                    <div class="timeline-item">
                        <div class="year">2010+</div>
                        <div class="content">移动互联网、响应式设计、前端框架时代</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Web基本原理 -->
        <section class="slide" id="slide-3">
            <div class="slide-content">
                <h2>Web基本原理</h2>
                <div class="web-architecture">
                    <div class="component client">
                        <h3>客户端 (Client)</h3>
                        <ul>
                            <li>浏览器 (Browser)</li>
                            <li>移动应用</li>
                            <li>桌面应用</li>
                        </ul>
                    </div>
                    <div class="arrow">→</div>
                    <div class="component server">
                        <h3>服务器 (Server)</h3>
                        <ul>
                            <li>Web服务器</li>
                            <li>应用服务器</li>
                            <li>数据库服务器</li>
                        </ul>
                    </div>
                </div>
                <div class="protocol">
                    <h3>通信协议：HTTP/HTTPS</h3>
                    <p>客户端通过HTTP协议向服务器发送请求，服务器返回响应</p>
                </div>
            </div>
        </section>

        <!-- HTTP协议基础 -->
        <section class="slide" id="slide-4">
            <div class="slide-content">
                <h2>HTTP协议基础</h2>
                <div class="http-info">
                    <div class="http-methods">
                        <h3>常用HTTP方法</h3>
                        <ul>
                            <li><strong>GET</strong> - 获取资源</li>
                            <li><strong>POST</strong> - 提交数据</li>
                            <li><strong>PUT</strong> - 更新资源</li>
                            <li><strong>DELETE</strong> - 删除资源</li>
                        </ul>
                    </div>
                    <div class="http-status">
                        <h3>常见状态码</h3>
                        <ul>
                            <li><strong>200</strong> - 成功</li>
                            <li><strong>404</strong> - 未找到</li>
                            <li><strong>500</strong> - 服务器错误</li>
                        </ul>
                    </div>
                </div>
                <div class="http-example">
                    <h3>HTTP请求示例</h3>
                    <pre><code>GET /index.html HTTP/1.1
Host: www.example.com
User-Agent: Mozilla/5.0
Accept: text/html</code></pre>
                </div>
            </div>
        </section>

        <!-- 开发环境配置 -->
        <section class="slide" id="slide-5">
            <div class="slide-content">
                <h2>开发环境配置</h2>
                <div class="tools-grid">
                    <div class="tool">
                        <h3>代码编辑器</h3>
                        <div class="tool-item">
                            <strong>VS Code</strong>
                            <p>推荐插件：</p>
                            <ul>
                                <li>Live Server</li>
                                <li>HTML CSS Support</li>
                                <li>JavaScript (ES6) code snippets</li>
                                <li>Prettier</li>
                            </ul>
                        </div>
                    </div>
                    <div class="tool">
                        <h3>浏览器</h3>
                        <div class="tool-item">
                            <strong>Chrome/Firefox</strong>
                            <p>开发者工具：</p>
                            <ul>
                                <li>Elements 面板</li>
                                <li>Console 面板</li>
                                <li>Network 面板</li>
                                <li>Sources 面板</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 第一个HTML页面 -->
        <section class="slide" id="slide-6">
            <div class="slide-content">
                <h2>第一个HTML页面</h2>
                <div class="code-demo">
                    <h3>基本HTML结构</h3>
                    <pre><code>&lt;!DOCTYPE html&gt;
&lt;html lang="zh-CN"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;我的第一个网页&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;h1&gt;欢迎来到Web世界！&lt;/h1&gt;
    &lt;p&gt;这是我的第一个HTML页面。&lt;/p&gt;
&lt;/body&gt;
&lt;/html&gt;</code></pre>
                </div>
                <div class="html-explanation">
                    <h3>结构说明</h3>
                    <ul>
                        <li><code>&lt;!DOCTYPE html&gt;</code> - 声明文档类型</li>
                        <li><code>&lt;html&gt;</code> - 根元素</li>
                        <li><code>&lt;head&gt;</code> - 文档头部，包含元数据</li>
                        <li><code>&lt;body&gt;</code> - 文档主体，包含可见内容</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- 课堂练习 -->
        <section class="slide" id="slide-7">
            <div class="slide-content">
                <h2>课堂练习</h2>
                <div class="exercise">
                    <h3>练习1：环境配置</h3>
                    <ol>
                        <li>安装VS Code编辑器</li>
                        <li>安装Live Server插件</li>
                        <li>打开浏览器开发者工具</li>
                    </ol>
                    
                    <h3>练习2：创建第一个网页</h3>
                    <ol>
                        <li>创建一个新的HTML文件</li>
                        <li>编写基本的HTML结构</li>
                        <li>添加标题和段落内容</li>
                        <li>使用Live Server预览页面</li>
                    </ol>
                    
                    <h3>练习3：使用开发者工具</h3>
                    <ol>
                        <li>在浏览器中打开你的网页</li>
                        <li>使用F12打开开发者工具</li>
                        <li>在Elements面板中查看HTML结构</li>
                        <li>在Console面板中输入简单的JavaScript代码</li>
                    </ol>
                </div>
            </div>
        </section>

        <!-- 总结 -->
        <section class="slide" id="slide-8">
            <div class="slide-content">
                <h2>本节课总结</h2>
                <div class="summary">
                    <div class="summary-item">
                        <h3>✓ 学习内容</h3>
                        <ul>
                            <li>Web发展历史和基本原理</li>
                            <li>HTTP协议基础知识</li>
                            <li>开发环境的配置方法</li>
                            <li>HTML页面的基本结构</li>
                        </ul>
                    </div>
                    <div class="summary-item">
                        <h3>📝 课后作业</h3>
                        <ul>
                            <li>完成开发环境配置</li>
                            <li>创建一个个人介绍页面</li>
                            <li>熟悉浏览器开发者工具</li>
                            <li>预习下节课内容：HTML5基础</li>
                        </ul>
                    </div>
                </div>
                <div class="next-week">
                    <h3>下周预告：HTML5基础</h3>
                    <p>我们将学习HTML5的各种标签和语义化标签的使用</p>
                </div>
            </div>
        </section>
    </div>

    <!-- 导航控制 -->
    <div class="navigation">
        <button id="prevBtn" onclick="changeSlide(-1)">← 上一页</button>
        <span id="slideCounter">1 / 8</span>
        <button id="nextBtn" onclick="changeSlide(1)">下一页 →</button>
    </div>

    <!-- 进度条 -->
    <div class="progress-bar">
        <div class="progress" id="progress"></div>
    </div>

    <script src="script.js"></script>
</body>
</html>
